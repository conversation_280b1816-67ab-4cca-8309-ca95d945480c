@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@keyframes spotlight {
  0% {
    opacity: 0;
    transform: translate(-72%, -62%) scale(0.5);
  }

  100% {
    opacity: 1;
    transform: translate(-50%, -40%) scale(1);
  }
}

:root {
  /* Animation */
  --animate-spotlight: spotlight 2s ease 0.75s 1 forwards;

  /* Black shades */
  --black: #000;
  --black-100: #000319;
  --black-200: rgba(17, 25, 40, 0.75);
  --black-300: rgba(255, 255, 255, 0.125);

  /* White shades */
  --white: #fff;
  --white-100: #bec1dd;
  --white-200: #c1c2d3;

  /* Blue, Purple */
  --blue-100: #e4ecff;
  --purple: #cbacf9;

  /* Semantic colors (you can adjust these as needed) */
  --border: 220 14% 95%;
  --input: 220 14% 95%;
  --ring: 220 14% 95%;
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;

  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;

  /* Border radius */
  --radius: 0.5rem;
}